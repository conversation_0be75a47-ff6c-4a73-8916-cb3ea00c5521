const WebSocket = require('ws');
const fs = require('fs');

// Configuration
const MARKET = '1HZ10V'; // Volatility 10 (1s)
const TARGET_HOURS = 24; // 24 hours = 1 day
const BATCH_SIZE = 5000; // Maximum ticks per request
const OUTPUT_FILE = 'past_day_ticks.json';
const DIGITS_FILE = 'past_day_digits.txt';

let allTicks = [];
let totalFetched = 0;
const targetTicks = TARGET_HOURS * 60 * 60; // 86,400 ticks for 24 hours

console.log('🚀 Starting extraction of PAST DAY TICKS from Volatility 10 (1s)...');
console.log('📊 Market:', MARKET);
console.log('🎯 Target period: 24 hours');
console.log('📈 Expected ticks: ~86,400 (1 per second)');
console.log('='.repeat(60));

function extractDigit(price) {
    // For Volatility 10 (1s), extract last digit from price * 100
    return Math.floor((price * 100) % 10);
}

function fetchBatch(endTime = 'latest') {
    return new Promise((resolve, reject) => {
        const ws = new WebSocket('wss://ws.derivws.com/websockets/v3?app_id=1089');
        
        ws.onopen = () => {
            console.log(`📡 Fetching batch... (${totalFetched} ticks collected)`);
            
            const request = {
                ticks_history: MARKET,
                count: BATCH_SIZE,
                end: endTime,
                adjust_start_time: 1,
                style: 'ticks'
            };
            
            ws.send(JSON.stringify(request));
        };
        
        ws.onmessage = (msg) => {
            try {
                const data = JSON.parse(msg.data);
                
                if (data.msg_type === 'history') {
                    const prices = data.history.prices;
                    const times = data.history.times;
                    
                    console.log(`✅ Received ${prices.length} ticks`);
                    
                    // Create tick objects with timestamp, price, and digit
                    const tickObjects = prices.map((price, index) => ({
                        timestamp: times[index],
                        price: parseFloat(price),
                        digit: extractDigit(parseFloat(price)),
                        datetime: new Date(times[index] * 1000).toISOString()
                    }));
                    
                    // Add to beginning of array (since we're going backwards in time)
                    allTicks.unshift(...tickObjects);
                    totalFetched += tickObjects.length;
                    
                    console.log(`📈 Total ticks collected: ${totalFetched}`);
                    console.log(`⏰ Latest tick time: ${tickObjects[tickObjects.length - 1].datetime}`);
                    console.log(`⏰ Earliest tick time: ${tickObjects[0].datetime}`);
                    
                    // Get the earliest timestamp for next batch
                    const earliestTime = times[0];
                    
                    ws.close();
                    resolve({
                        tickObjects,
                        earliestTime,
                        hasMore: prices.length === BATCH_SIZE && totalFetched < targetTicks
                    });
                } else if (data.error) {
                    console.error('❌ API Error:', data.error.message);
                    ws.close();
                    reject(new Error(data.error.message));
                }
            } catch (error) {
                console.error('❌ Parse Error:', error);
                ws.close();
                reject(error);
            }
        };
        
        ws.onerror = (error) => {
            console.error('❌ WebSocket Error:', error);
            reject(error);
        };
        
        // Timeout after 30 seconds
        setTimeout(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
                reject(new Error('Request timeout'));
            }
        }, 30000);
    });
}

async function fetchPastDayTicks() {
    let endTime = 'latest';
    let hasMore = true;
    
    try {
        while (hasMore && totalFetched < targetTicks) {
            const result = await fetchBatch(endTime);
            
            if (result.hasMore && totalFetched < targetTicks) {
                // Use the earliest time minus 1 second for next batch
                endTime = result.earliestTime - 1;
                
                // Add a small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 1000));
            } else {
                hasMore = false;
            }
        }
        
        console.log('\n🎉 Extraction completed!');
        console.log(`📊 Total ticks extracted: ${allTicks.length}`);
        
        if (allTicks.length > 0) {
            const firstTick = allTicks[0];
            const lastTick = allTicks[allTicks.length - 1];
            
            console.log(`⏰ Time range: ${firstTick.datetime} to ${lastTick.datetime}`);
            
            // Calculate actual time span
            const timeSpanHours = (lastTick.timestamp - firstTick.timestamp) / 3600;
            console.log(`⌛ Actual time span: ${timeSpanHours.toFixed(2)} hours`);
        }
        
        // Save complete tick data
        fs.writeFileSync(OUTPUT_FILE, JSON.stringify(allTicks, null, 2));
        console.log(`💾 Complete tick data saved to: ${OUTPUT_FILE}`);
        
        // Extract and save just the digits
        const digits = allTicks.map(tick => tick.digit);
        fs.writeFileSync(DIGITS_FILE, digits.join(''));
        console.log(`🔢 Digits sequence saved to: ${DIGITS_FILE}`);
        
        // Display statistics
        displayStatistics();
        
        return allTicks;
        
    } catch (error) {
        console.error('❌ Extraction failed:', error.message);
        
        if (allTicks.length > 0) {
            console.log(`💾 Saving partial data (${allTicks.length} ticks)...`);
            fs.writeFileSync(`partial_${OUTPUT_FILE}`, JSON.stringify(allTicks, null, 2));
            
            const digits = allTicks.map(tick => tick.digit);
            fs.writeFileSync(`partial_${DIGITS_FILE}`, digits.join(''));
        }
        
        throw error;
    }
}

function displayStatistics() {
    const digits = allTicks.map(tick => tick.digit);
    const digitCounts = Array(10).fill(0);
    digits.forEach(digit => digitCounts[digit]++);
    
    console.log('\n📈 DIGIT STATISTICS:');
    console.log('Digit | Count | Percentage | Per Hour');
    console.log('------|-------|------------|----------');
    
    const timeSpanHours = allTicks.length > 0 ? 
        (allTicks[allTicks.length - 1].timestamp - allTicks[0].timestamp) / 3600 : 1;
    
    digitCounts.forEach((count, digit) => {
        const percentage = ((count / digits.length) * 100).toFixed(2);
        const perHour = (count / timeSpanHours).toFixed(1);
        console.log(`  ${digit}   | ${count.toString().padStart(5)} | ${percentage.padStart(6)}%   | ${perHour.padStart(6)}`);
    });
    
    // Price statistics
    const prices = allTicks.map(tick => tick.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length;
    
    console.log('\n💰 PRICE STATISTICS:');
    console.log(`Min Price: ${minPrice.toFixed(3)}`);
    console.log(`Max Price: ${maxPrice.toFixed(3)}`);
    console.log(`Avg Price: ${avgPrice.toFixed(3)}`);
    console.log(`Price Range: ${(maxPrice - minPrice).toFixed(3)}`);
    
    // Even/Odd statistics
    const evenCount = digits.filter(d => d % 2 === 0).length;
    const oddCount = digits.length - evenCount;
    
    console.log('\n⚖️ EVEN/ODD DISTRIBUTION:');
    console.log(`Even: ${evenCount} (${((evenCount / digits.length) * 100).toFixed(2)}%)`);
    console.log(`Odd:  ${oddCount} (${((oddCount / digits.length) * 100).toFixed(2)}%)`);
}

// Start extraction
fetchPastDayTicks()
    .then(() => {
        console.log('\n✅ Process completed successfully!');
        console.log('📁 Files created:');
        console.log(`   • ${OUTPUT_FILE} - Complete tick data with timestamps`);
        console.log(`   • ${DIGITS_FILE} - Digit sequence for analysis`);
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Process failed:', error.message);
        process.exit(1);
    });
