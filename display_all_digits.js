const fs = require('fs');

// Read the digits file
const digitsFile = 'v10_past_day_digits.txt';

try {
    const allDigits = fs.readFileSync(digitsFile, 'utf8');
    
    console.log('='.repeat(80));
    console.log('ALL 86,300 VOLATILITY 10 (1s) DIGITS - PAST 24 HOURS');
    console.log('='.repeat(80));
    console.log('Copy the digits below (select all and copy):');
    console.log('='.repeat(80));
    console.log('');
    
    // Display all digits in one continuous string
    console.log(allDigits);
    
    console.log('');
    console.log('='.repeat(80));
    console.log(`Total digits: ${allDigits.length}`);
    console.log('='.repeat(80));
    
} catch (error) {
    console.error('Error reading digits file:', error.message);
    console.log('Make sure the file v10_past_day_digits.txt exists in the current directory.');
}
