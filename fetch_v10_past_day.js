const WebSocket = require('ws');
const fs = require('fs');

// Configuration for Volatility 10 (1s) - Past 24 Hours
const MARKET = '1HZ10V'; // Volatility 10 (1s)
const BATCH_SIZE = 5000; // Maximum ticks per request
const OUTPUT_TICKS = 'v10_past_day_ticks.json';
const OUTPUT_DIGITS = 'v10_past_day_digits.txt';
const OUTPUT_SUMMARY = 'v10_past_day_summary.json';

let allTicks = [];
let totalFetched = 0;
let batchCount = 0;

console.log('🚀 FETCHING ALL PAST DAY TICKS - VOLATILITY 10 (1s)');
console.log('='.repeat(70));
console.log('📊 Market Symbol: 1HZ10V');
console.log('⏰ Target Period: Last 24 hours');
console.log('🎯 Goal: Extract ALL available ticks');
console.log('📈 Expected: ~86,400 ticks (1 per second for 24 hours)');
console.log('='.repeat(70));

function extractDigit(price) {
    // Extract last digit from price * 100 for V10
    return Math.floor((price * 100) % 10);
}

function formatTimestamp(timestamp) {
    return new Date(timestamp * 1000).toISOString();
}

function fetchTickBatch(endTime = 'latest') {
    return new Promise((resolve, reject) => {
        const ws = new WebSocket('wss://ws.derivws.com/websockets/v3?app_id=1089');
        
        ws.onopen = () => {
            batchCount++;
            console.log(`\n📡 Batch ${batchCount}: Requesting ${BATCH_SIZE} ticks...`);
            console.log(`📊 Total collected so far: ${totalFetched} ticks`);
            
            const request = {
                ticks_history: MARKET,
                count: BATCH_SIZE,
                end: endTime,
                adjust_start_time: 1,
                style: 'ticks'
            };
            
            ws.send(JSON.stringify(request));
        };
        
        ws.onmessage = (msg) => {
            try {
                const data = JSON.parse(msg.data);
                
                if (data.msg_type === 'history') {
                    const prices = data.history.prices;
                    const times = data.history.times;
                    
                    if (prices.length === 0) {
                        console.log('⚠️ No more data available');
                        ws.close();
                        resolve({ ticks: [], hasMore: false });
                        return;
                    }
                    
                    console.log(`✅ Received: ${prices.length} ticks`);
                    
                    // Create detailed tick objects
                    const ticks = prices.map((price, index) => {
                        const timestamp = times[index];
                        const priceFloat = parseFloat(price);
                        return {
                            timestamp: timestamp,
                            datetime: formatTimestamp(timestamp),
                            price: priceFloat,
                            digit: extractDigit(priceFloat),
                            even_odd: extractDigit(priceFloat) % 2 === 0 ? 'even' : 'odd'
                        };
                    });
                    
                    // Add to beginning (going backwards in time)
                    allTicks.unshift(...ticks);
                    totalFetched += ticks.length;
                    
                    const latestTick = ticks[ticks.length - 1];
                    const earliestTick = ticks[0];
                    
                    console.log(`⏰ Latest: ${latestTick.datetime}`);
                    console.log(`⏰ Earliest: ${earliestTick.datetime}`);
                    console.log(`📈 Running total: ${totalFetched} ticks`);
                    
                    // Check if we should continue (24 hours = 86400 seconds)
                    const now = Math.floor(Date.now() / 1000);
                    const oneDayAgo = now - (24 * 60 * 60);
                    const earliestTime = times[0];
                    
                    const hasMore = prices.length === BATCH_SIZE && earliestTime > oneDayAgo;
                    
                    if (!hasMore) {
                        console.log(`\n🎯 Reached 24-hour limit or end of data`);
                        console.log(`📅 Cutoff time: ${formatTimestamp(oneDayAgo)}`);
                    }
                    
                    ws.close();
                    resolve({
                        ticks: ticks,
                        earliestTime: earliestTime,
                        hasMore: hasMore
                    });
                    
                } else if (data.error) {
                    console.error('❌ API Error:', data.error.message);
                    ws.close();
                    reject(new Error(data.error.message));
                }
            } catch (error) {
                console.error('❌ Parse Error:', error);
                ws.close();
                reject(error);
            }
        };
        
        ws.onerror = (error) => {
            console.error('❌ WebSocket Error:', error);
            reject(error);
        };
        
        // Timeout after 45 seconds
        setTimeout(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
                reject(new Error('Request timeout - batch took too long'));
            }
        }, 45000);
    });
}

async function fetchAllPastDayTicks() {
    let endTimestamp = 'latest';
    let hasMore = true;
    const startTime = Date.now();

    try {
        console.log('\n🔄 Starting data extraction...\n');

        while (hasMore) {
            const result = await fetchTickBatch(endTimestamp);

            if (result.hasMore) {
                // Move to next batch (1 second before earliest time)
                endTimestamp = result.earliestTime - 1;

                // Rate limiting delay
                console.log('⏳ Waiting 2 seconds before next batch...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
                hasMore = false;
            }
        }

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(1);
        
        console.log('\n🎉 EXTRACTION COMPLETED!');
        console.log('='.repeat(50));
        console.log(`📊 Total ticks extracted: ${allTicks.length}`);
        console.log(`⏱️ Extraction time: ${duration} seconds`);
        console.log(`📦 Total batches: ${batchCount}`);
        
        if (allTicks.length > 0) {
            const firstTick = allTicks[0];
            const lastTick = allTicks[allTicks.length - 1];
            
            console.log(`\n📅 DATA RANGE:`);
            console.log(`   Start: ${firstTick.datetime}`);
            console.log(`   End:   ${lastTick.datetime}`);
            
            const timeSpan = lastTick.timestamp - firstTick.timestamp;
            const hours = (timeSpan / 3600).toFixed(2);
            console.log(`   Span:  ${hours} hours`);
            
            // Save all data
            await saveAllData();
            
            // Display comprehensive statistics
            displayComprehensiveStats();
        }
        
        return allTicks;
        
    } catch (error) {
        console.error('\n❌ EXTRACTION FAILED:', error.message);
        
        if (allTicks.length > 0) {
            console.log(`\n💾 Saving partial data (${allTicks.length} ticks)...`);
            await saveAllData('partial_');
        }
        
        throw error;
    }
}

async function saveAllData(prefix = '') {
    try {
        // Save complete tick data
        const ticksFile = prefix + OUTPUT_TICKS;
        fs.writeFileSync(ticksFile, JSON.stringify(allTicks, null, 2));
        console.log(`\n💾 Complete tick data: ${ticksFile}`);
        
        // Save digits sequence
        const digits = allTicks.map(tick => tick.digit);
        const digitsFile = prefix + OUTPUT_DIGITS;
        fs.writeFileSync(digitsFile, digits.join(''));
        console.log(`🔢 Digits sequence: ${digitsFile}`);
        
        // Create summary
        const summary = createSummary();
        const summaryFile = prefix + OUTPUT_SUMMARY;
        fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
        console.log(`📋 Summary report: ${summaryFile}`);
        
    } catch (error) {
        console.error('❌ Error saving files:', error.message);
    }
}

function createSummary() {
    const digits = allTicks.map(tick => tick.digit);
    const prices = allTicks.map(tick => tick.price);
    
    return {
        extraction_info: {
            total_ticks: allTicks.length,
            market: MARKET,
            extraction_date: new Date().toISOString(),
            time_range: {
                start: allTicks[0]?.datetime,
                end: allTicks[allTicks.length - 1]?.datetime,
                span_hours: allTicks.length > 0 ? 
                    ((allTicks[allTicks.length - 1].timestamp - allTicks[0].timestamp) / 3600).toFixed(2) : 0
            }
        },
        digit_statistics: {
            counts: digits.reduce((acc, digit) => {
                acc[digit] = (acc[digit] || 0) + 1;
                return acc;
            }, {}),
            even_count: digits.filter(d => d % 2 === 0).length,
            odd_count: digits.filter(d => d % 2 === 1).length
        },
        price_statistics: {
            min: Math.min(...prices),
            max: Math.max(...prices),
            average: prices.reduce((a, b) => a + b, 0) / prices.length,
            range: Math.max(...prices) - Math.min(...prices)
        }
    };
}

function displayComprehensiveStats() {
    const digits = allTicks.map(tick => tick.digit);
    const digitCounts = Array(10).fill(0);
    digits.forEach(digit => digitCounts[digit]++);
    
    console.log('\n📊 COMPREHENSIVE STATISTICS');
    console.log('='.repeat(50));
    
    console.log('\n🔢 DIGIT FREQUENCY:');
    console.log('Digit | Count | Percentage | Expected');
    console.log('------|-------|------------|----------');
    
    digitCounts.forEach((count, digit) => {
        const percentage = ((count / digits.length) * 100).toFixed(2);
        const expected = '10.00%';
        const deviation = (percentage - 10).toFixed(2);
        console.log(`  ${digit}   | ${count.toString().padStart(5)} | ${percentage.padStart(6)}%   | ${expected} (${deviation > 0 ? '+' : ''}${deviation}%)`);
    });
    
    const evenCount = digits.filter(d => d % 2 === 0).length;
    const oddCount = digits.length - evenCount;
    
    console.log('\n⚖️ EVEN/ODD DISTRIBUTION:');
    console.log(`Even: ${evenCount} (${((evenCount / digits.length) * 100).toFixed(2)}%)`);
    console.log(`Odd:  ${oddCount} (${((oddCount / digits.length) * 100).toFixed(2)}%)`);
    
    const prices = allTicks.map(tick => tick.price);
    console.log('\n💰 PRICE ANALYSIS:');
    console.log(`Min Price: ${Math.min(...prices).toFixed(4)}`);
    console.log(`Max Price: ${Math.max(...prices).toFixed(4)}`);
    console.log(`Avg Price: ${(prices.reduce((a, b) => a + b, 0) / prices.length).toFixed(4)}`);
    console.log(`Range: ${(Math.max(...prices) - Math.min(...prices)).toFixed(4)}`);
}

// Start the extraction
console.log('⚡ Initializing extraction...\n');

fetchAllPastDayTicks()
    .then(() => {
        console.log('\n✅ SUCCESS! All past day ticks extracted.');
        console.log('\n📁 Generated Files:');
        console.log(`   • ${OUTPUT_TICKS} - Complete tick data`);
        console.log(`   • ${OUTPUT_DIGITS} - Digit sequence`);
        console.log(`   • ${OUTPUT_SUMMARY} - Summary statistics`);
        console.log('\n🚀 Ready for analysis!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n💥 FAILED:', error.message);
        process.exit(1);
    });
