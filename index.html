<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mrduke Updated tool</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
  <script>
            // Disable right-click
        document.addEventListener('contextmenu', (e) => e.preventDefault());

        function ctrlShiftKey(e, keyCode) {
        return e.ctrlKey && e.shiftKey && e.keyCode === keyCode.charCodeAt(0);
        }

        document.onkeydown = (e) => {
        // Disable F12, Ctrl + Shift + I, Ctrl + Shift + J, Ctrl + U
        if (
            event.keyCode === 123 ||
            ctrlShiftKey(e, 'I') ||
            ctrlShiftKey(e, 'J') ||
            ctrlShiftKey(e, 'C') ||
            (e.ctrlKey && e.keyCode === 'U'.charCodeAt(0))
        )
            return false;
        };
</script>
  <style>
    :root {
      --primary: #3b82f6;
      --primary-dark: #2563eb;
      --secondary: #10b981;
      --danger: #ef4444;
      --dark: #1e293b;
      --light: #f8fafc;
      --gray: #64748b;
      --gray-light: #e2e8f0;
      --border-radius: 0.75rem;
      --transition: all 0.2s cubic-bezier(.4,0,.2,1);
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --card-bg: rgba(255, 255, 255, 0.9);
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      color: var(--dark);
      line-height: 1.6;
      min-height: 100vh;
      -webkit-font-smoothing: antialiased;
    }

    .container {
      max-width: 1000px;
      padding: 1rem;
    }

    .card {
      border: none;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      margin-bottom: 1.5rem;
      background: var(--card-bg);
      backdrop-filter: blur(10px);
      transition: var(--transition);
      overflow: hidden;
    }
    
    .card:hover {
      box-shadow: var(--shadow-hover);
      transform: translateY(-0.25rem);
    }

    .card-header {
      background-color: rgba(59, 130, 246, 0.05);
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);
      padding: 1rem 1.25rem;
    }

    .card-title {
      font-weight: 600;
      color: var(--primary-dark);
      margin-bottom: 0;
      font-size: 1.1rem;
    }

    .card-body {
      padding: 1.25rem;
    }

    .form-control, .form-select {
      border-radius: 0.5rem;
      padding: 0.75rem 1rem;
      border: 1px solid var(--gray-light);
      background-color: white;
      transition: var(--transition);
      font-size: 0.95rem;
      height: auto;
    }
    
    .form-control:focus, .form-select:focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      background-color: white;
    }

    .form-label {
      font-weight: 500;
      color: var(--dark);
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    .current-value-container {
      background: linear-gradient(135deg, rgba(226, 232, 240, 0.5) 0%, rgba(241, 245, 249, 0.5) 100%);
      border-radius: var(--border-radius);
      padding: 1rem;
      margin-bottom: 1.5rem;
      box-shadow: var(--shadow);
    }

    .current-value-label {
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--gray);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 0.5rem;
    }

    .current-value {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0;
      color: var(--primary-dark);
      line-height: 1;
      transition: color 0.3s ease;
    }

    .stats-container {
      display: flex;
      justify-content: space-between;
      margin-top: 1rem;
    }

    .stat-item {
      text-align: center;
      flex: 1;
    }

    .stat-label {
      font-size: 0.85rem;
      font-weight: 600;
      color: var(--gray);
      margin-bottom: 0.25rem;
    } 

    .stat-value {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--dark);
    }

    .pattern-display {
      display: flex;
      flex-wrap: wrap;
      gap: 0.35rem;
      justify-content: center;
      padding: 0.75rem;
      background: rgba(241, 245, 249, 0.5);
      border-radius: 0.5rem;
      min-height: 3rem;
      box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
    }

    .pattern-item {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      font-weight: 600;
      font-size: 0.85rem;
      transition: transform 0.2s ease;
      color: white;
    }

    .pattern-item:hover {
      transform: scale(1.1);
    }

    .E { color: var(--primary-dark); background-color: rgba(59, 130, 246, 0.1); }
    .O { color: var(--danger); background-color: rgba(239, 68, 68, 0.1); }
    .R { color: var(--secondary); background-color: rgba(16, 185, 129, 0.1); }
    .F, .U { color: var(--danger); background-color: rgba(239, 68, 68, 0.1); }
    .Ov { color: var(--secondary); background-color: rgba(16, 185, 129, 0.1); }

    /* Digit-specific colors */
    .digit-0 { background-color: #3b82f6; } /* blue */
    .digit-1 { background-color: #ef4444; } /* red */
    .digit-2 { background-color: #10b981; } /* green */
    .digit-3 { background-color: #f59e0b; } /* yellow */
    .digit-4 { background-color: #8b5cf6; } /* purple */
    .digit-5 { background-color: #ec4899; } /* pink */
    .digit-6 { background-color: #14b8a6; } /* teal */
    .digit-7 { background-color: #f97316; } /* orange */
    .digit-8 { background-color: #64748b; } /* gray */
    .digit-9 { background-color: #84cc16; } /* lime */

    .chart-container {
      height: 350px;
      width: 100%;
      position: relative;
    }

    .guide-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      border-radius: 0.5rem;
      overflow: hidden;
      box-shadow: var(--shadow);
      background-color: white;
    }

    .guide-table th {
      background-color: var(--primary);
      color: white;
      font-weight: 600;
      padding: 0.75rem;
      text-align: center;
    }

    .guide-table td {
      padding: 0.75rem;
      text-align: center;
      background-color: white;
      border-bottom: 1px solid var(--gray-light);
    }

    .guide-table tr:nth-child(even) td {
      background-color: rgba(241, 245, 249, 0.5);
    }

    .section-title {
      font-size: 1rem;
      font-weight: 600;
      margin: 1.5rem 0 1rem;
      color: var(--primary-dark);
      position: relative;
      padding-left: 0.75rem;
    }

    .section-title::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: var(--primary);
      border-radius: 3px;
    }

    .badge-info {
      background-color: var(--primary);
    }

    .text-primary { color: var(--primary) !important; }
    .text-secondary { color: var(--secondary) !important; }
    .text-danger { color: var(--danger) !important; }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .container {
        padding: 0.75rem;
      }
      
      .current-value {
        font-size: 2rem;
      }
      
      .stat-value {
        font-size: 1.25rem;
      }
      
      .pattern-item {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.8rem;
      }
      
      .chart-container {
        height: 280px;
      }
      
      .guide-table th, 
      .guide-table td {
        padding: 0.5rem;
        font-size: 0.85rem;
      }
      
      .section-title {
        font-size: 0.95rem;
        margin: 1.25rem 0 0.75rem;
      }
    }

    @media (max-width: 576px) {
      .current-value {
        font-size: 1.75rem;
      }
      
      .stat-value {
        font-size: 1.1rem;
      }
      
      .pattern-item {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.75rem;
      }
      
      .chart-container {
        height: 250px;
      }
      
      .form-control, .form-select {
        padding: 0.65rem 0.75rem;
        font-size: 0.9rem;
      }
      
      .card-body {
        padding: 1rem;
      }
    }

    /* Animation for current value changes */
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .value-updated {
      animation: pulse 0.5s ease;
    }

    /* Tooltip styles */
    .tooltip-inner {
      max-width: 250px;
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
      background-color: var(--dark);
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: var(--gray-light);
    }

    ::-webkit-scrollbar-thumb {
      background: var(--primary);
      border-radius: 4px;
    }

    /* Digit Circles Styles */
    .digit-circles-container {
      padding: 30px 20px;
      background: #f8fafc;
      border-radius: 12px;
      margin: 20px 0;
    }

    .digit-circles-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 30px;
      max-width: 700px;
      margin: 0 auto;
    }

    .digit-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .circle-progress {
      position: relative;
      margin-bottom: 12px;
    }

    .progress-ring {
      transform: rotate(-90deg);
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    }

    .progress-ring-circle {
      stroke: #000000;
      stroke-width: 6;
      transition: all 0.3s ease;
    }

    .progress-ring-progress {
      stroke: #000000;
      stroke-dasharray: 226.19; /* 2 * π * 36 */
      stroke-dashoffset: 226.19;
      transition: stroke-dashoffset 0.6s ease;
      stroke-linecap: round;
      stroke-width: 6;
    }

    /* Yellow outer ring */
    .progress-ring-outer {
      stroke: #fbbf24;
      stroke-width: 8;
      fill: transparent;
      r: 42;
      cx: 40;
      cy: 40;
    }

    .circle-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #1e293b;
    }

    .digit-number {
      font-size: 20px;
      font-weight: 700;
      line-height: 1;
      color: #334155;
    }

    .digit-percentage {
      font-size: 10px;
      opacity: 0.7;
      line-height: 1;
      margin-top: 2px;
      color: #64748b;
    }

    .digit-stats {
      color: #475569;
      font-size: 13px;
      font-weight: 600;
    }

    .digit-count {
      font-weight: 700;
      margin-bottom: 4px;
      color: #334155;
    }

    .digit-deviation {
      font-size: 12px;
      font-weight: 600;
      padding: 2px 8px;
      border-radius: 12px;
      background: rgba(255,255,255,0.8);
    }

    .digit-deviation.positive {
      color: #059669;
      background: rgba(16, 185, 129, 0.1);
    }

    .digit-deviation.negative {
      color: #dc2626;
      background: rgba(239, 68, 68, 0.1);
    }

    .digit-deviation.neutral {
      color: #6b7280;
      background: rgba(107, 114, 128, 0.1);
    }

    /* Dark Theme Styles */
    [data-theme="dark"] {
      --bg-primary: #1a1a1a;
      --bg-secondary: #2d2d2d;
      --bg-tertiary: #3a3a3a;
      --text-primary: #ffffff;
      --text-secondary: #b0b0b0;
      --border-color: #404040;
      --card-bg: #2d2d2d;
      --input-bg: #3a3a3a;
      --input-border: #555555;
    }

    /* Light Theme (default) */
    [data-theme="light"], :root {
      --bg-primary: #ffffff;
      --bg-secondary: #f8f9fa;
      --bg-tertiary: #e9ecef;
      --text-primary: #212529;
      --text-secondary: #6c757d;
      --border-color: #dee2e6;
      --card-bg: #ffffff;
      --input-bg: #ffffff;
      --input-border: #ced4da;
    }

    /* Apply theme variables */
    body {
      background-color: var(--bg-primary) !important;
      color: var(--text-primary) !important;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .card {
      background-color: var(--card-bg) !important;
      border-color: var(--border-color) !important;
      color: var(--text-primary) !important;
    }

    .form-control, .form-select {
      background-color: var(--input-bg) !important;
      border-color: var(--input-border) !important;
      color: var(--text-primary) !important;
    }

    .form-control:focus, .form-select:focus {
      background-color: var(--input-bg) !important;
      border-color: var(--primary-color) !important;
      color: var(--text-primary) !important;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table {
      color: var(--text-primary) !important;
    }

    .table th {
      border-color: var(--border-color) !important;
      background-color: var(--bg-secondary) !important;
    }

    .table td {
      border-color: var(--border-color) !important;
    }

    /* Theme toggle button */
    .theme-toggle {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      background: var(--card-bg);
      border: 2px solid var(--border-color);
      border-radius: 50px;
      padding: 8px 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: var(--text-primary);
    }

    .theme-toggle:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .theme-icon {
      font-size: 16px;
      transition: transform 0.3s ease;
    }

    [data-theme="dark"] .theme-toggle .sun-icon {
      display: none;
    }

    [data-theme="light"] .theme-toggle .moon-icon {
      display: none;
    }

    /* Dark theme specific adjustments */
    [data-theme="dark"] .navbar {
      background-color: var(--bg-secondary) !important;
    }

    [data-theme="dark"] .btn-outline-primary {
      color: #4dabf7;
      border-color: #4dabf7;
    }

    [data-theme="dark"] .btn-outline-primary:hover {
      background-color: #4dabf7;
      border-color: #4dabf7;
      color: #000;
    }

    [data-theme="dark"] .alert-info {
      background-color: #1e3a5f;
      border-color: #2c5aa0;
      color: #b8daff;
    }

    /* No background effects for current digit - only circle color changes */

    /* All circles black outer only by default */
    .digit-circle .progress-ring-progress {
      stroke: transparent;
      stroke-width: 6;
    }

    .digit-circle .progress-ring-circle {
      stroke: #000000;
      stroke-width: 6;
    }

    /* Current digit - full yellow (both outer and inner) */
    .digit-circle.current-digit .progress-ring-progress {
      stroke: #fbbf24 !important;
      stroke-width: 6 !important;
    }

    .digit-circle.current-digit .progress-ring-circle {
      stroke: #fbbf24 !important;
      stroke-width: 6 !important;
    }

    /* Highest performing digit - full green (both outer and inner) */
    .digit-circle[data-performance="highest"] .progress-ring-progress {
      stroke: #22c55e !important;
      stroke-width: 6 !important;
    }

    .digit-circle[data-performance="highest"] .progress-ring-circle {
      stroke: #22c55e !important;
      stroke-width: 6 !important;
    }

    /* Lowest performing digit - full red (both outer and inner) */
    .digit-circle[data-performance="lowest"] .progress-ring-progress {
      stroke: #ef4444 !important;
      stroke-width: 6 !important;
    }

    .digit-circle[data-performance="lowest"] .progress-ring-circle {
      stroke: #ef4444 !important;
      stroke-width: 6 !important;
    }

    /* Current digit overrides performance colors */
    .digit-circle.current-digit[data-performance="highest"] .progress-ring-progress,
    .digit-circle.current-digit[data-performance="lowest"] .progress-ring-progress {
      stroke: #fbbf24 !important;
    }

    .digit-circle.current-digit[data-performance="highest"] .progress-ring-circle,
    .digit-circle.current-digit[data-performance="lowest"] .progress-ring-circle {
      stroke: #fbbf24 !important;
    }

    /* All circles have consistent styling - no special colors */
    .digit-circle {
      background: transparent;
      transform: scale(1);
    }

    .digit-circle .circle-content {
      color: #1e293b;
    }

    .digit-circle .digit-number {
      color: #334155;
      font-weight: 700;
      text-shadow: none;
    }

    .digit-circle .digit-stats {
      color: #475569;
      font-weight: 600;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .digit-circles-container {
        padding: 20px 15px;
      }

      .digit-circles-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        max-width: 500px;
      }

      .progress-ring {
        width: 70px;
        height: 70px;
      }

      .progress-ring circle {
        r: 31;
        cx: 35;
        cy: 35;
      }

      .digit-number {
        font-size: 18px;
      }

      .digit-percentage {
        font-size: 9px;
      }

      .digit-stats {
        font-size: 12px;
      }


    }

    @media (max-width: 480px) {
      .digit-circles-container {
        padding: 15px 10px;
      }

      .digit-circles-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        max-width: 350px;
      }

      .progress-ring {
        width: 65px;
        height: 65px;
      }

      .progress-ring circle {
        r: 28;
        cx: 32.5;
        cy: 32.5;
      }

      .digit-number {
        font-size: 16px;
      }

      .digit-percentage {
        font-size: 8px;
      }

      .digit-stats {
        font-size: 11px;
      }

      .digit-deviation {
        font-size: 11px;
        padding: 1px 6px;
      }


    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--primary-dark);
    }

    /* Probability Cards Styles */
    .probability-section {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(16, 185, 129, 0.02) 100%);
      border-radius: var(--border-radius);
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .probability-controls {
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }

    .probability-section-header {
      margin-bottom: 1rem;
    }

    .probability-cards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .probability-card {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      transition: var(--transition);
      cursor: pointer;
      border: 2px solid transparent;
      overflow: hidden;
    }

    .probability-card:hover {
      box-shadow: var(--shadow-hover);
      transform: translateY(-2px);
      border-color: var(--primary);
    }

    .probability-card.selected {
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .probability-card .card-header {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
      padding: 1rem;
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .probability-label {
      font-weight: 600;
      color: var(--dark);
      font-size: 0.95rem;
    }

    .probability-value {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--primary-dark);
    }

    .probability-card .card-body {
      padding: 1rem;
    }

    .probability-bar-container {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.75rem;
    }

    .probability-bar {
      flex: 1;
      height: 8px;
      background-color: rgba(226, 232, 240, 0.5);
      border-radius: 4px;
      overflow: hidden;
    }

    .probability-fill {
      height: 100%;
      border-radius: 4px;
      transition: width 0.6s ease;
    }

    .probability-fill.high-prob {
      background: linear-gradient(90deg, var(--secondary) 0%, #22c55e 100%);
    }

    .probability-fill.medium-prob {
      background: linear-gradient(90deg, #f59e0b 0%, #eab308 100%);
    }

    .probability-fill.low-prob {
      background: linear-gradient(90deg, var(--danger) 0%, #dc2626 100%);
    }

    .probability-trend {
      font-size: 1rem;
    }

    .probability-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 0.5rem;
    }

    .probability-stats .stat-item {
      text-align: center;
      padding: 0.25rem;
    }

    .probability-stats .stat-label {
      display: block;
      color: var(--gray);
      font-weight: 500;
      margin-bottom: 0.125rem;
    }

    .probability-stats .stat-value {
      display: block;
      color: var(--dark);
      font-weight: 600;
    }

    /* Enhanced Button Styles */
    .btn {
      border-radius: 0.5rem;
      font-weight: 500;
      transition: var(--transition);
      border: none;
      padding: 0.5rem 1rem;
    }

    .btn-outline-primary {
      color: var(--primary);
      border: 1px solid var(--primary);
      background: transparent;
    }

    .btn-outline-primary:hover {
      background: var(--primary);
      color: white;
      transform: translateY(-1px);
      box-shadow: var(--shadow);
    }

    .btn-outline-secondary {
      color: var(--gray);
      border: 1px solid var(--gray-light);
      background: transparent;
    }

    .btn-outline-secondary:hover {
      background: var(--gray);
      color: white;
      transform: translateY(-1px);
      box-shadow: var(--shadow);
    }

    /* Enhanced Alert Styles */
    .alert {
      border-radius: var(--border-radius);
      border: none;
      padding: 1rem 1.25rem;
      margin-bottom: 1rem;
    }

    .alert-primary {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
      color: var(--primary-dark);
      border-left: 4px solid var(--primary);
    }

    /* Enhanced Card Animations */
    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .card {
      animation: slideInUp 0.6s ease-out;
    }

    /* Additional Mobile Responsiveness */
    @media (max-width: 768px) {
      .probability-cards-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }
      
      .probability-controls {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
      }
      
      .probability-controls .btn {
        width: 100%;
      }
      
      .probability-stats {
        grid-template-columns: 1fr;
        gap: 0.25rem;
      }
      
      .theme-toggle {
        position: fixed;
        top: 10px;
        right: 10px;
        padding: 6px 12px;
        font-size: 12px;
      }
    }

    @media (max-width: 576px) {
      .container {
        padding: 0.5rem;
      }
      
      .probability-section {
        padding: 1rem;
        margin-bottom: 1rem;
      }
      
      .probability-card .card-header,
      .probability-card .card-body {
        padding: 0.75rem;
      }
      
      .probability-value {
        font-size: 1.1rem;
      }
      
      .stats-container {
        flex-direction: column;
        gap: 1rem;
      }
    }

    /* Over/Under Analysis Styles */
    .over-under-chart-container {
      background: linear-gradient(135deg, rgba(241, 245, 249, 0.5) 0%, rgba(226, 232, 240, 0.3) 100%);
      border-radius: var(--border-radius);
      padding: 1.5rem;
      margin-top: 1rem;
    }

    .chart-header h6 {
      color: var(--primary-dark);
      font-weight: 600;
    }

    .probability-bars {
      margin: 1.5rem 0;
    }

    .probability-bar-item {
      background: white;
      border-radius: 0.5rem;
      padding: 1rem;
      box-shadow: var(--shadow);
      transition: var(--transition);
    }

    .probability-bar-item:hover {
      box-shadow: var(--shadow-hover);
      transform: translateY(-1px);
    }

    .probability-label {
      font-weight: 600;
      color: var(--dark);
      font-size: 0.95rem;
    }

    .probability-percentage {
      font-weight: 700;
      font-size: 1.1rem;
    }

    .progress {
      border-radius: 0.5rem;
      overflow: hidden;
      background-color: rgba(226, 232, 240, 0.3);
    }

    .progress-bar {
      border-radius: 0.5rem;
      transition: width 0.6s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .progress-text {
      color: white;
      font-weight: 600;
      font-size: 0.85rem;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .over-under-stats {
      background: white;
      border-radius: var(--border-radius);
      padding: 1.5rem;
      box-shadow: var(--shadow);
    }

    .stat-card {
      padding: 1rem;
      border-radius: 0.5rem;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
      transition: var(--transition);
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }

    .stat-card .stat-value {
      font-size: 1.75rem;
      font-weight: 700;
      margin-bottom: 0.25rem;
      color: var(--primary-dark);
    }

    .stat-card .stat-label {
      font-size: 0.85rem;
      font-weight: 500;
      color: var(--gray);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    /* Dark theme adjustments for Over/Under section */
    [data-theme="dark"] .over-under-chart-container {
      background: linear-gradient(135deg, rgba(45, 45, 45, 0.5) 0%, rgba(58, 58, 58, 0.3) 100%);
    }

    [data-theme="dark"] .probability-bar-item {
      background: var(--card-bg);
      color: var(--text-primary);
    }

    [data-theme="dark"] .over-under-stats {
      background: var(--card-bg);
      color: var(--text-primary);
    }

    [data-theme="dark"] .stat-card {
      background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%);
    }

    /* Over/Under Prediction Comparison Styles */
    .over-under-prediction-container {
      background: linear-gradient(135deg, rgba(241, 245, 249, 0.5) 0%, rgba(226, 232, 240, 0.3) 100%);
      border-radius: var(--border-radius);
      padding: 1.5rem;
    }

    .prediction-bars {
      margin: 1.5rem 0;
    }

    .prediction-bar-item {
      background: white;
      border-radius: 0.75rem;
      padding: 1.25rem;
      box-shadow: var(--shadow);
      transition: var(--transition);
      border: 1px solid rgba(226, 232, 240, 0.5);
    }

    .prediction-bar-item:hover {
      box-shadow: var(--shadow-hover);
      transform: translateY(-2px);
    }

    .prediction-label {
      font-weight: 600;
      color: var(--dark);
      font-size: 1rem;
    }

    .prediction-percentage {
      font-weight: 700;
      font-size: 1.25rem;
    }

    .prediction-progress-container {
      background-color: rgba(226, 232, 240, 0.3);
      border-radius: 0.75rem;
      height: 40px;
      position: relative;
      overflow: hidden;
      margin-top: 0.75rem;
    }

    .prediction-progress-bar {
      height: 100%;
      border-radius: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: width 0.8s ease;
      position: relative;
    }

    .prediction-progress-bar.over-prediction {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    .prediction-progress-bar.under-prediction {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }

    .prediction-progress-text {
      color: white;
      font-weight: 700;
      font-size: 0.95rem;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .prediction-stats {
      background: white;
      border-radius: var(--border-radius);
      padding: 1.5rem;
      box-shadow: var(--shadow);
      border: 1px solid rgba(226, 232, 240, 0.5);
    }

    .prediction-stats .stat-card {
      padding: 1rem;
      border-radius: 0.5rem;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
      transition: var(--transition);
      border: 1px solid rgba(226, 232, 240, 0.3);
    }

    .prediction-stats .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }

    .prediction-stats .stat-card .stat-value {
      font-size: 1.75rem;
      font-weight: 700;
      margin-bottom: 0.25rem;
      color: var(--primary-dark);
    }

    .prediction-stats .stat-card .stat-label {
      font-size: 0.85rem;
      font-weight: 500;
      color: var(--gray);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    /* Dark theme adjustments for Prediction section */
    [data-theme="dark"] .over-under-prediction-container {
      background: linear-gradient(135deg, rgba(45, 45, 45, 0.5) 0%, rgba(58, 58, 58, 0.3) 100%);
    }

    [data-theme="dark"] .prediction-bar-item {
      background: var(--card-bg);
      color: var(--text-primary);
      border-color: var(--border-color);
    }

    [data-theme="dark"] .prediction-stats {
      background: var(--card-bg);
      color: var(--text-primary);
      border-color: var(--border-color);
    }

    [data-theme="dark"] .prediction-stats .stat-card {
      background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%);
      border-color: var(--border-color);
    }

    /* Responsive adjustments for Over/Under section */
    @media (max-width: 768px) {
      .over-under-chart-container {
        padding: 1rem;
      }

      .over-under-stats {
        padding: 1rem;
      }

      .stat-card .stat-value {
        font-size: 1.5rem;
      }

      .probability-percentage {
        font-size: 1rem;
      }

      .over-under-prediction-container {
        padding: 1rem;
      }

      .prediction-bar-item {
        padding: 1rem;
      }

      .prediction-progress-container {
        height: 35px;
      }

      .prediction-percentage {
        font-size: 1.1rem;
      }

      .prediction-stats {
        padding: 1rem;
      }

      .prediction-stats .stat-card .stat-value {
        font-size: 1.5rem;
      }
    }
  </style>
</head>
<body data-theme="light">
  <!-- Theme Toggle Button -->
  <div class="theme-toggle" onclick="toggleTheme()">
    <span class="theme-icon sun-icon">☀️</span>
    <span class="theme-icon moon-icon">🌙</span>
    <span class="theme-text">Theme</span>
  </div>

  <div class="container py-3">
    <div class="row justify-content-center">
      <div class="col-12">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h1 class="h4 mb-0 text-primary fw-bold">Mrduke Updated tool</h1>
          <span class="badge bg-primary bg-opacity-10 text-primary">Pro</span>
        </div>
        
        <!-- Configuration Card -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-sliders me-2"></i>Analysis Configuration
            </h5>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="market" class="form-label">Synthetic Market</label>
                  <select class="form-select" id="market" onchange="changeMarket()">
                    <option value="R_10" selected>Volatility 10</option>
                    <option value="1HZ10V">Volatility 10 (1s)</option>
                    <option value="R_25">Volatility 25</option>
                    <option value="1HZ25V">Volatility 25 (1s)</option>
                    <option value="R_50">Volatility 50</option>
                    <option value="1HZ50V">Volatility 50 (1s)</option>
                    <option value="R_75">Volatility 75</option>
                    <option value="1HZ75V">Volatility 75 (1s)</option>
                    <option value="R_100">Volatility 100</option>
                    <option value="1HZ100V">Volatility 100 (1s)</option>
                    <option value="JD10">Jump Index 10</option>
                    <option value="JD25">Jump Index 25</option>
                    <option value="JD50">Jump Index 50</option>
                    <option value="JD75">Jump Index 75</option>
                    <option value="JD100">Jump Index 100</option>
                    <option value="RDBEAR">Bear Market</option>
                    <option value="RDBULL">Bull Market</option>
                  </select>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-group">
                  <label for="contract_type" class="form-label">Trade Type</label>
                  <select class="form-select" id="contract_type" onchange="changeContractType()">
                    <option value="1">Rise/Fall</option>
                    <option value="0">Even/Odd</option>
                    <option value="2">Over/Under</option>
                    <option value="3" selected>Digit Frequency</option>
                  </select>
                </div>
              </div>
              
              <div class="col-md-6" id="over_under" style="display:none;">
                <div class="row g-2">
                  <div class="col-6">
                    <div class="form-group">
                      <label for="over_input" class="form-label">Over Value</label>
                      <input type="number" class="form-control" id="over_input" step="1" value="4" min="0" max="9" oninput="over_under()" required>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="form-group">
                      <label for="under_input" class="form-label">Under Value</label>
                      <input type="number" class="form-control" id="under_input" step="1" value="5" min="0" max="9" oninput="over_under()" required>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-6" id="digit_frequency_predictions" style="display:none;">
                <div class="row g-2">
                  <div class="col-6">
                    <div class="form-group">
                      <label for="digit_over_prediction" class="form-label">Over Prediction</label>
                      <input type="number" class="form-control" id="digit_over_prediction" step="1" value="4" min="0" max="9" oninput="updateDigitPredictions()" required>
                      <small class="text-muted">Predict digits > this value</small>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="form-group">
                      <label for="digit_under_prediction" class="form-label">Under Prediction</label>
                      <input type="number" class="form-control" id="digit_under_prediction" step="1" value="5" min="0" max="9" oninput="updateDigitPredictions()" required>
                      <small class="text-muted">Predict digits < this value</small>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-group">
                  <label for="ticks" class="form-label">Number of Ticks to Analyze</label>
                  <input type="number" class="form-control" id="ticks" step="1" value="120" min="1" max="5000" required>
                  <small class="text-muted">Max 5000 ticks</small>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Current Value and Stats -->
        <div class="current-value-container mb-4">
          <div class="current-value-label">Current Price</div>
          <div class="current-value text-center fw-bold" id="current">0</div>
          
          <div class="stats-container">
            <div class="stat-item">
              <div class="stat-label text-primary" id="even_label_title">Even</div>
              <div class="stat-value text-primary" id="even_label">0</div>
            </div>
            <div class="stat-item">
              <div class="stat-label text-danger" id="odd_label_title">Odd</div>
              <div class="stat-value text-danger" id="odd_label">0</div>
            </div>
          </div>
        </div>
        

        
        <!-- Chart -->
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-bar-chart-line me-2"></i>Probability Analysis
            </h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="myChart"></canvas>
            </div>

            <!-- Custom Digit Circles Display -->
            <div id="digitCircles" class="digit-circles-container" style="display: none;">
              <div class="digit-circles-grid">
                <div class="digit-circle" data-digit="0" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">0</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="1" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">1</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="2" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">2</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="3" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">3</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="4" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">4</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="5" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">5</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="6" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">6</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="7" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">7</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="8" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">8</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>

                <div class="digit-circle" data-digit="9" data-performance="neutral">
                  <div class="circle-progress">
                    <svg class="progress-ring" width="80" height="80">
                      <circle class="progress-ring-circle" fill="transparent" r="36" cx="40" cy="40"/>
                      <circle class="progress-ring-progress" fill="transparent" r="36" cx="40" cy="40"/>
                    </svg>
                    <div class="circle-content">
                      <div class="digit-number">9</div>
                      <div class="digit-percentage">0.0%</div>
                    </div>
                  </div>
                  <div class="digit-stats">
                    <div class="digit-count">0</div>
                    <div class="digit-deviation neutral">0.0%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Over/Under Prediction Comparison -->
        <div class="card mb-4" id="overUnderPredictionSection" style="display: none;">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-bar-chart-line me-2"></i>Over vs Under Prediction Analysis
            </h5>
          </div>
          <div class="card-body">
            <div class="over-under-prediction-container">
              <div class="chart-header mb-3 text-center">
                <h6 class="mb-1">Prediction Performance Comparison</h6>
                <small class="text-muted">Based on current digit frequency analysis</small>
              </div>

              <div class="prediction-bars">
                <!-- Over Prediction Bar -->
                <div class="prediction-bar-item mb-4">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="prediction-label">Over Prediction</span>
                    <span class="prediction-percentage text-primary" id="over_prediction_percentage">40.9%</span>
                  </div>
                  <div class="prediction-progress-container">
                    <div class="prediction-progress-bar over-prediction" id="over_prediction_bar" style="width: 40.9%;">
                      <span class="prediction-progress-text">40.9%</span>
                    </div>
                  </div>
                </div>

                <!-- Under Prediction Bar -->
                <div class="prediction-bar-item">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="prediction-label">Under Prediction</span>
                    <span class="prediction-percentage text-danger" id="under_prediction_percentage">59.1%</span>
                  </div>
                  <div class="prediction-progress-container">
                    <div class="prediction-progress-bar under-prediction" id="under_prediction_bar" style="width: 59.1%;">
                      <span class="prediction-progress-text">59.1%</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Prediction Statistics -->
              <div class="prediction-stats mt-4">
                <div class="row text-center">
                  <div class="col-md-3">
                    <div class="stat-card">
                      <div class="stat-value" id="prediction_total_digits">0</div>
                      <div class="stat-label">Total Digits</div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="stat-card">
                      <div class="stat-value text-primary" id="over_prediction_count">0</div>
                      <div class="stat-label">Over Count</div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="stat-card">
                      <div class="stat-value text-danger" id="under_prediction_count">0</div>
                      <div class="stat-label">Under Count</div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="stat-card">
                      <div class="stat-value text-success" id="prediction_accuracy">0%</div>
                      <div class="stat-label">Accuracy</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Over/Under Analysis Section -->
        <div class="card mb-4" id="overUnderSection" style="display: none;">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-bar-chart-line me-2"></i>Over/Under Analysis
            </h5>
          </div>
          <div class="card-body">
            <!-- Over/Under Input Controls -->
            <div class="row g-3 mb-4">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="over_value_input" class="form-label">Over Value</label>
                  <input type="number" class="form-control" id="over_value_input" step="1" value="4" min="0" max="9" oninput="updateOverUnderAnalysis()" required>
                  <small class="text-muted">Digits greater than this value</small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="under_value_input" class="form-label">Under Value</label>
                  <input type="number" class="form-control" id="under_value_input" step="1" value="5" min="0" max="9" oninput="updateOverUnderAnalysis()" required>
                  <small class="text-muted">Digits less than this value</small>
                </div>
              </div>
            </div>

            <!-- Over/Under Chart Display -->
            <div class="over-under-chart-container">
              <div class="chart-header mb-3">
                <h6 class="mb-0">Probability Analysis</h6>
                <small class="text-muted">Based on recent tick data</small>
              </div>
              
              <div class="probability-bars">
                <div class="probability-bar-item mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="probability-label">Over <span id="over_display_value">4</span></span>
                    <span class="probability-percentage text-success" id="over_percentage">50.0%</span>
                  </div>
                  <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 50%" id="over_progress_bar">
                      <span class="progress-text">50.0%</span>
                    </div>
                  </div>
                </div>

                <div class="probability-bar-item">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="probability-label">Under <span id="under_display_value">5</span></span>
                    <span class="probability-percentage text-danger" id="under_percentage">50.0%</span>
                  </div>
                  <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-danger" role="progressbar" style="width: 50%" id="under_progress_bar">
                      <span class="progress-text">50.0%</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Statistics Display -->
              <div class="over-under-stats mt-4">
                <div class="row text-center">
                  <div class="col-md-4">
                    <div class="stat-card">
                      <div class="stat-value" id="total_ticks_analyzed">0</div>
                      <div class="stat-label">Total Ticks</div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="stat-card">
                      <div class="stat-value text-success" id="over_count">0</div>
                      <div class="stat-label">Over Count</div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="stat-card">
                      <div class="stat-value text-danger" id="under_count">0</div>
                      <div class="stat-label">Under Count</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        

      </div>
    </div>
  </div>

  <!-- Hidden inputs -->
  <input type="hidden" id="even" value="0">
  <input type="hidden" id="odd" value="0">
  <input type="hidden" id="rise" value="0">
  <input type="hidden" id="fall" value="0">
  <input type="hidden" id="over" value="0">
  <input type="hidden" id="under" value="0">
  <input type="hidden" id="digit0" value="0">
  <input type="hidden" id="digit1" value="0">
  <input type="hidden" id="digit2" value="0">
  <input type="hidden" id="digit3" value="0">
  <input type="hidden" id="digit4" value="0">
  <input type="hidden" id="digit5" value="0">
  <input type="hidden" id="digit6" value="0">
  <input type="hidden" id="digit7" value="0">
  <input type="hidden" id="digit8" value="0">
  <input type="hidden" id="digit9" value="0">
  <input type="hidden" id="previous" value="0">
  <input type="hidden" id="subid" value="774cbfef-72c8-c8e4-42c5-189e941d2379">
  <input type="hidden" id="old_ticks" value="10">
  <input type="hidden" id="token" value="">

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
  
  <script>
    // Initialize variables
    let ctx = document.getElementById("myChart").getContext("2d");
    let sockets = [];
    let ticks_current = [];
    let isLaptop = !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && 
                   screen.width >= 768 && 
                   screen.height >= 768;
    let myChart;

    // Helper functions
    const countDecimals = (value) => {
      const numStr = String(value);
      return numStr.includes(".") ? numStr.split(".")[1].length : 0;
    };

    const removeAllClients = () => {
      sockets.forEach(s => s.close());
      sockets = [];
    };

    const findSmallest = (arr) => {
      let smallest = arr[0], index = 0;
      for (let i = 1; i < arr.length; i++) {
        if (arr[i] < smallest) {
          smallest = arr[i];
          index = i;
        }
      }
      return { smallest, index };
    };

    const findLargest = (arr) => {
      let largest = arr[0], index = 0;
      for (let i = 1; i < arr.length; i++) {
        if (arr[i] > largest) {
          largest = arr[i];
          index = i;
        }
      }
      return { largest, index };
    };

    const formatQuote = (quote, decimals) => {
      const temp = countDecimals(quote);
      if (temp === 0) return quote + "." + "0".repeat(decimals);
      if (temp === 1) return quote + "0".repeat(decimals - 1);
      if (temp === 2 && decimals > 2) return quote + "0".repeat(decimals - 2);
      if (temp === 3 && decimals > 3) return quote + "0".repeat(decimals - 3);
      return quote;
    };

    const updateCurrentValueColor = (type) => {
      const currentEl = document.getElementById("current");
      currentEl.classList.remove("text-primary", "text-secondary", "text-danger");
      currentEl.classList.add(`text-${type}`);
      currentEl.classList.add("value-updated");
      setTimeout(() => currentEl.classList.remove("value-updated"), 500);
    };

    const updatePatternDisplay = (pattern) => {
      const patternDisplay = document.getElementById("pattern_display");
      patternDisplay.innerHTML = pattern.map(p => 
        `<span class="pattern-item ${p.class}" data-bs-toggle="tooltip" title="${p.tooltip || ''}">${p.char}</span>`
      ).join('');
      // Initialize tooltips
      const tooltipTriggerList = [].slice.call(patternDisplay.querySelectorAll('[data-bs-toggle="tooltip"]'));
      tooltipTriggerList.map(el => new bootstrap.Tooltip(el));
    };

    const updateStats = (value1, value2) => {
      const total = value1 + value2;
      const percent1 = total > 0 ? ((value1 / total) * 100).toFixed(1) : 0;
      const percent2 = total > 0 ? ((value2 / total) * 100).toFixed(1) : 0;
      
      document.getElementById("even_label").textContent = value1;
      document.getElementById("odd_label").textContent = value2;
      
      if (myChart && myChart.data && myChart.data.datasets) {
        myChart.data.datasets[0].data = [percent1, percent2];
        myChart.update();
      }
    };

    const updateDigitStats = (digits) => {
      const total = digits.reduce((a, b) => a + b, 0);
      const percentages = digits.map(d => total > 0 ? ((d / total) * 100).toFixed(1) : 0);

      // Find highest and lowest digits
      const resultS = findSmallest(digits);
      const resultL = findLargest(digits);

      document.getElementById("even_label").textContent = resultL.index;
      document.getElementById("odd_label").textContent = resultS.index;

      // Update digit circles if visible
      const digitCircles = document.getElementById('digitCircles');
      if (digitCircles && digitCircles.style.display !== 'none') {
        updateDigitCircles(digits, total);
      } else if (myChart && myChart.data && myChart.data.datasets) {
        // Update chart data for non-digit charts
        myChart.data.datasets[0].data = percentages;

        // Update colors - highlight highest and lowest
        const bgColors = Array(10).fill("rgba(59, 130, 246, 0.8)");
        const borderColors = Array(10).fill("rgba(59, 130, 246, 1)");

        bgColors[resultS.index] = "rgba(239, 68, 68, 0.8)";
        borderColors[resultS.index] = "rgba(239, 68, 68, 1)";
        bgColors[resultL.index] = "rgba(16, 185, 129, 0.8)";
        borderColors[resultL.index] = "rgba(16, 185, 129, 1)";

        myChart.data.datasets[0].backgroundColor = bgColors;
        myChart.data.datasets[0].borderColor = borderColors;
        myChart.update();
      }
    };

    const updateDigitCircles = (digits, total) => {
      // Find highest and lowest performing digits
      let highestCount = -1;
      let lowestCount = Infinity;
      let highestDigit = -1;
      let lowestDigit = -1;

      digits.forEach((count, digit) => {
        if (count > highestCount) {
          highestCount = count;
          highestDigit = digit;
        }
        if (count < lowestCount && count > 0) {
          lowestCount = count;
          lowestDigit = digit;
        }
      });

      digits.forEach((count, digit) => {
        const percentage = total > 0 ? ((count / total) * 100) : 0;
        const deviation = percentage - 10; // Expected is 10% for each digit

        const digitCircle = document.querySelector(`[data-digit="${digit}"]`);
        if (digitCircle) {
          // Update percentage display
          const percentageElement = digitCircle.querySelector('.digit-percentage');
          percentageElement.textContent = `${percentage.toFixed(1)}%`;

          // Update count display
          const countElement = digitCircle.querySelector('.digit-count');
          countElement.textContent = count;

          // Update deviation display
          const deviationElement = digitCircle.querySelector('.digit-deviation');
          const deviationText = deviation >= 0 ? `+${deviation.toFixed(1)}%` : `${deviation.toFixed(1)}%`;
          deviationElement.textContent = deviationText;

          // Set deviation class and performance attribute
          let deviationClass = 'digit-deviation';
          let performanceLevel = 'neutral';

          // Special cases for highest and lowest
          if (digit === highestDigit && total > 10) {
            performanceLevel = 'highest';
            deviationClass += ' positive';
          } else if (digit === lowestDigit && total > 10) {
            performanceLevel = 'lowest';
            deviationClass += ' negative';
          } else if (Math.abs(deviation) < 1) {
            deviationClass += ' neutral';
            performanceLevel = 'neutral';
          } else if (deviation > 1) {
            deviationClass += ' positive';
            performanceLevel = 'high';
          } else {
            deviationClass += ' negative';
            performanceLevel = 'low';
          }

          deviationElement.className = deviationClass;
          digitCircle.setAttribute('data-performance', performanceLevel);

          // Update progress ring with smooth animation
          const progressRing = digitCircle.querySelector('.progress-ring-progress');
          const circumference = 2 * Math.PI * 36; // r = 36
          const progress = Math.min(percentage, 100); // Cap at 100%
          const offset = circumference - (progress / 100) * circumference;

          // Animate the progress
          progressRing.style.strokeDashoffset = offset;

          // Add subtle hover effect
          digitCircle.style.transition = 'transform 0.3s ease';
          digitCircle.addEventListener('mouseenter', () => {
            if (performanceLevel !== 'highest' && performanceLevel !== 'lowest') {
              digitCircle.style.transform = 'scale(1.05)';
            }
          });
          digitCircle.addEventListener('mouseleave', () => {
            if (performanceLevel !== 'highest' && performanceLevel !== 'lowest') {
              digitCircle.style.transform = 'scale(1)';
            }
          });
        }
      });
    };

    // Function to mark the current digit circle as yellow
    const setCurrentDigit = (digit) => {
      // Remove current-digit class from all circles
      document.querySelectorAll('.digit-circle').forEach(circle => {
        circle.classList.remove('current-digit');
      });

      // Add current-digit class to the target circle
      const targetCircle = document.querySelector(`[data-digit="${digit}"]`);
      if (targetCircle) {
        targetCircle.classList.add('current-digit');
      }
    };

    // Initialize chart
    const initChart = (type, labels) => {
      // Show/hide appropriate display
      const chartContainer = document.querySelector('.chart-container');
      const digitCircles = document.getElementById('digitCircles');

      if (type === "digits") {
        chartContainer.style.display = 'none';
        digitCircles.style.display = 'block';

        // Clear any current digit highlighting
        setTimeout(() => {
          document.querySelectorAll('.digit-circle').forEach(circle => {
            circle.classList.remove('current-digit');
          });
        }, 100);

        return; // Don't create chart for digits
      } else {
        chartContainer.style.display = 'block';
        digitCircles.style.display = 'none';

        // Clear current digit highlighting when switching away from digits
        document.querySelectorAll('.digit-circle').forEach(circle => {
          circle.classList.remove('current-digit');
        });
      }

      if (myChart) {
        myChart.destroy();
      }

      ctx = document.getElementById("myChart").getContext("2d");

      let chartConfig = {
        type: "bar",
        plugins: [ChartDataLabels],
        data: {
          labels: labels,
          datasets: [{
            data: labels.map(() => 0),
            borderWidth: 1,
            borderRadius: 5,
          }]
        },
        options: {
          indexAxis: "y",
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return `${context.dataset.label || context.label}: ${context.raw}%`;
                }
              }
            },
            datalabels: {
              formatter: (value) => `${value}%`,
              clamp: true,
              anchor: "end",
              align: "start",
              color: "white",
              font: { weight: "bold", size: 12 }
            }
          },
          scales: {
            x: {
              display: false,
              grid: { display: false },
              max: 100
            },
            y: {
              display: true,
              grid: { display: false },
              max: 100
            }
          }
        }
      };

      // Customize based on chart type
      switch(type) {
        case "even_odd":
          chartConfig.data.datasets[0].backgroundColor = ["rgba(59, 130, 246, 0.8)", "rgba(239, 68, 68, 0.8)"];
          chartConfig.data.datasets[0].borderColor = ["rgba(59, 130, 246, 1)", "rgba(239, 68, 68, 1)"];
          break;
          
        case "rise_fall":
          chartConfig.data.datasets[0].backgroundColor = ["rgba(16, 185, 129, 0.8)", "rgba(239, 68, 68, 0.8)"];
          chartConfig.data.datasets[0].borderColor = ["rgba(16, 185, 129, 1)", "rgba(239, 68, 68, 1)"];
          break;
          
        case "over_under":
          chartConfig.data.datasets[0].backgroundColor = ["rgba(16, 185, 129, 0.8)", "rgba(239, 68, 68, 0.8)"];
          chartConfig.data.datasets[0].borderColor = ["rgba(16, 185, 129, 1)", "rgba(239, 68, 68, 1)"];
          break;
          

      }

      myChart = new Chart(ctx, chartConfig);
    };

    // Main analysis functions
    const even_odd = () => {
      removeAllClients();
      initChart("even_odd", ["Even", "Odd"]);

      document.getElementById("even_label_title").textContent = "Even";
      document.getElementById("odd_label_title").textContent = "Odd";
      
      const market = document.getElementById("market").value;
      const ws = new WebSocket("wss://ws.derivws.com/websockets/v3?app_id=1089");
      sockets.push(ws);
      
      ws.onopen = () => ws.send(JSON.stringify({ authorize: document.getElementById("token").value }));
      
      ws.onmessage = (msg) => {
        try {
          const data = JSON.parse(msg.data);
          
          if (data.msg_type === "authorize") {
            ws.send(JSON.stringify({ forget_all: "ticks" }));
          } 
          else if (data.msg_type === "forget_all") {
            ws.send(JSON.stringify({ forget: document.getElementById("subid").value }));
          } 
          else if (data.msg_type === "forget") {
            document.getElementById("even").value = 0;
            document.getElementById("odd").value = 0;
            ws.send(JSON.stringify({
              ticks_history: market,
              count: document.getElementById("ticks").value,
              end: "latest",
              adjust_start_time: 1,
              style: "ticks",
            }));
          } 
          else if (data.msg_type === "history") {
            let even = 0, odd = 0;
            ticks_current = [];
            
            data.history.prices.forEach(price => {
              let digit = 0;
              if (market === "RDBEAR" || market === "RDBULL" || market === "R_50" || market === "R_75") {
                digit = (price * 10000) % 10;
              } else if (market === "R_10" || market === "R_25") {
                digit = (price * 1000) % 10;
              } else {
                digit = (price * 100) % 10;
              }
              
              ticks_current.push(digit);
              digit % 2 === 0 ? even++ : odd++;
            });
            
            updateStats(even, odd);
            ws.send(JSON.stringify({ ticks: market, subscribe: 1 }));
          } 
          else if (data.msg_type === "tick") {
            if (data.echo_req.ticks === market) {
              document.getElementById("subid").value = data.subscription.id;
              let even = 0, odd = 0;
              let digit = 0;
              const quote = data.tick.quote;
              
              // Process digit based on market
              if (market === "RDBEAR" || market === "RDBULL" || market === "R_50" || market === "R_75") {
                digit = (quote * 10000) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 4);
              } else if (market === "R_10" || market === "R_25") {
                digit = (quote * 1000) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 3);
              } else {
                digit = (quote * 100) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 2);
              }
              
              // Update current value color
              updateCurrentValueColor(digit % 2 === 0 ? "primary" : "danger");
              
              // Maintain last 50 digits
              if (ticks_current.length >= 50) ticks_current.shift();
              ticks_current.push(digit);
              
              // Count even/odd
              ticks_current.forEach(d => d % 2 === 0 ? even++ : odd++);
              
              // Update stats and chart
              updateStats(even, odd);
              
              // Update pattern display
              updatePatternDisplay(ticks_current.slice(-50).map(d => ({
                char: d % 2 === 0 ? 'E' : 'O',
                class: d % 2 === 0 ? 'E' : 'O',
                tooltip: `Digit: ${d} (${d % 2 === 0 ? 'Even' : 'Odd'})`
              })));
            }
          } 
          else if (data.error) {
            console.error("WebSocket error:", data.error.message);
          }
        } catch (error) {
          console.error("Error processing WebSocket message:", error);
        }
      };
      
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    };

    const rise_fall = () => {
      removeAllClients();
      initChart("rise_fall", ["Rise", "Fall"]);

      document.getElementById("even_label_title").textContent = "Rise";
      document.getElementById("odd_label_title").textContent = "Fall";
      
      const market = document.getElementById("market").value;
      const ws = new WebSocket("wss://ws.derivws.com/websockets/v3?app_id=1089");
      sockets.push(ws);
      
      ws.onopen = () => ws.send(JSON.stringify({ authorize: document.getElementById("token").value }));
      
      ws.onmessage = (msg) => {
        try {
          const data = JSON.parse(msg.data);
          
          if (data.msg_type === "authorize") {
            ws.send(JSON.stringify({ forget_all: "ticks" }));
          } 
          else if (data.msg_type === "forget_all") {
            ws.send(JSON.stringify({ forget: document.getElementById("subid").value }));
          } 
          else if (data.msg_type === "forget") {
            document.getElementById("rise").value = 0;
            document.getElementById("fall").value = 0;
            document.getElementById("previous").value = 0;
            ws.send(JSON.stringify({
              ticks_history: market,
              count: document.getElementById("ticks").value,
              end: "latest",
            }));
          } 
          else if (data.msg_type === "history") {
            let rise = 0, fall = 0;
            let previous = parseFloat(document.getElementById("previous").value);
            ticks_current = [];
            
            data.history.prices.forEach(price => {
              ticks_current.push(price);
              if (previous > price) fall++;
              else if (previous < price) rise++;
              previous = price;
            });
            
            document.getElementById("previous").value = previous;
            updateStats(rise, fall);
            ws.send(JSON.stringify({ ticks: market, subscribe: 1 }));
          } 
          else if (data.msg_type === "tick") {
            if (data.echo_req.ticks === market) {
              document.getElementById("subid").value = data.subscription.id;
              let rise = 0, fall = 0;
              const quote = data.tick.quote;
              
              // Format current value display
              if (market === "RDBEAR" || market === "RDBULL" || market === "R_50" || market === "R_75") {
                document.getElementById("current").innerHTML = formatQuote(quote, 4);
              } else if (market === "R_10" || market === "R_25") {
                document.getElementById("current").innerHTML = formatQuote(quote, 3);
              } else {
                document.getElementById("current").innerHTML = formatQuote(quote, 2);
              }
              
              // Update current value color
              const prevValue = ticks_current.length > 0 ? ticks_current[ticks_current.length - 1] : parseFloat(document.getElementById("previous").value);
              updateCurrentValueColor(quote > prevValue ? "secondary" : "danger");
              
              // Maintain last 50 ticks
              if (ticks_current.length >= 50) ticks_current.shift();
              ticks_current.push(quote);
              
              // Count rise/fall
              ticks_current.forEach((tick, i) => {
                if (i === 0) {
                  const prev = parseFloat(document.getElementById("previous").value);
                  if (prev > tick) fall++;
                  else if (prev < tick) rise++;
                } else {
                  if (ticks_current[i-1] > tick) fall++;
                  else if (ticks_current[i-1] < tick) rise++;
                }
              });
              
              document.getElementById("previous").value = quote;
              updateStats(rise, fall);
              
              // Update pattern display
              const pattern = [];
              for (let i = 1; i < ticks_current.length; i++) {
                pattern.push({
                  char: ticks_current[i-1] > ticks_current[i] ? 'F' : 'R',
                  class: ticks_current[i-1] > ticks_current[i] ? 'F' : 'R',
                  tooltip: `From ${ticks_current[i-1]} to ${ticks_current[i]}`
                });
              }
              updatePatternDisplay(pattern.slice(-50));
            }
          } 
          else if (data.error) {
            console.error("WebSocket error:", data.error.message);
          }
        } catch (error) {
          console.error("Error processing WebSocket message:", error);
        }
      };
      
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    };

    const over_under = () => {
      removeAllClients();
      initChart("over_under", ["Over", "Under"]);

      document.getElementById("even_label_title").textContent = "Over";
      document.getElementById("odd_label_title").textContent = "Under";
      
      const overVal = parseInt(document.getElementById("over_input").value);
      const underVal = parseInt(document.getElementById("under_input").value);
      const market = document.getElementById("market").value;
      const ws = new WebSocket("wss://ws.derivws.com/websockets/v3?app_id=1089");
      sockets.push(ws);
      
      ws.onopen = () => ws.send(JSON.stringify({ authorize: document.getElementById("token").value }));
      
      ws.onmessage = (msg) => {
        try {
          const data = JSON.parse(msg.data);
          
          if (data.msg_type === "authorize") {
            ws.send(JSON.stringify({ forget_all: "ticks" }));
          } 
          else if (data.msg_type === "forget_all") {
            ws.send(JSON.stringify({ forget: document.getElementById("subid").value }));
          } 
          else if (data.msg_type === "forget") {
            document.getElementById("over").value = 0;
            document.getElementById("under").value = 0;
            ws.send(JSON.stringify({
              ticks_history: market,
              count: document.getElementById("ticks").value,
              end: "latest",
            }));
          } 
          else if (data.msg_type === "history") {
            let over = 0, under = 0;
            ticks_current = [];
            
            data.history.prices.forEach(price => {
              let digit = 0;
              if (market === "RDBEAR" || market === "RDBULL" || market === "R_50" || market === "R_75") {
                digit = (price * 10000) % 10;
              } else if (market === "R_10" || market === "R_25") {
                digit = (price * 1000) % 10;
              } else {
                digit = (price * 100) % 10;
              }
              
              ticks_current.push(digit);
              if (digit > overVal) over++;
              if (digit < underVal) under++;
            });
            
            updateStats(over, under);
            updateOverUnderAnalysis();
            ws.send(JSON.stringify({ ticks: market, subscribe: 1 }));
          } 
          else if (data.msg_type === "tick") {
            if (data.echo_req.ticks === market) {
              document.getElementById("subid").value = data.subscription.id;
              let over = 0, under = 0;
              let digit = 0;
              const quote = data.tick.quote;
              
              // Process digit and format display
              if (market === "RDBEAR" || market === "RDBULL" || market === "R_50" || market === "R_75") {
                digit = (quote * 10000) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 4);
              } else if (market === "R_10" || market === "R_25") {
                digit = (quote * 1000) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 3);
              } else {
                digit = (quote * 100) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 2);
              }
              
              // Update current value color
              updateCurrentValueColor(digit > overVal ? "secondary" : (digit < underVal ? "danger" : "primary"));
              
              // Maintain last 50 digits
              if (ticks_current.length >= 50) ticks_current.shift();
              ticks_current.push(digit);
              
              // Count over/under
              ticks_current.forEach(d => {
                if (d > overVal) over++;
                if (d < underVal) under++;
              });
              updateStats(over, under);
              updateOverUnderAnalysis();
              
              
              // Update pattern display
              updatePatternDisplay(ticks_current.slice(-50).map(d => ({
                char: d > overVal ? 'Ov' : (d < underVal ? 'U' : '-'),
                class: d > overVal ? 'Ov' : (d < underVal ? 'U' : '-'),
                tooltip: `Digit: ${d} (${d > overVal ? 'Over' : d < underVal ? 'Under' : 'Neutral'})`
              })));
            }
          } 
          else if (data.error) {
            console.error("WebSocket error:", data.error.message);
          }
        } catch (error) {
          console.error("Error processing WebSocket message:", error);
        }
      };
      
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    };

    const match_differs = () => {
      removeAllClients();
      initChart("digits", ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]);

      // Get prediction values
      const overPrediction = parseInt(document.getElementById("digit_over_prediction").value);
      const underPrediction = parseInt(document.getElementById("digit_under_prediction").value);

      document.getElementById("even_label_title").textContent = `Over ${overPrediction}`;
      document.getElementById("odd_label_title").textContent = `Under ${underPrediction}`;
      
      const market = document.getElementById("market").value;
      const ws = new WebSocket("wss://ws.derivws.com/websockets/v3?app_id=1089");
      sockets.push(ws);
      
      ws.onopen = () => ws.send(JSON.stringify({ authorize: document.getElementById("token").value }));
      
      ws.onmessage = (msg) => {
        try {
          const data = JSON.parse(msg.data);
          
          if (data.msg_type === "authorize") {
            ws.send(JSON.stringify({ forget_all: "ticks" }));
          } 
          else if (data.msg_type === "forget_all") {
            ws.send(JSON.stringify({ forget: document.getElementById("subid").value }));
          } 
          else if (data.msg_type === "forget") {
            for (let i = 0; i <= 9; i++) {
              document.getElementById(`digit${i}`).value = 0;
            }
            ws.send(JSON.stringify({
              ticks_history: market,
              count: document.getElementById("ticks").value,
              end: "latest",
            }));
          } 
          else if (data.msg_type === "history") {
            const digits = Array(10).fill(0);
            let overCount = 0, underCount = 0;
            ticks_current = [];

            const overPrediction = parseInt(document.getElementById("digit_over_prediction").value);
            const underPrediction = parseInt(document.getElementById("digit_under_prediction").value);

            data.history.prices.forEach(price => {
              let digit = 0;
              if (market === "RDBEAR" || market === "RDBULL" || market === "R_50" || market === "R_75") {
                digit = (price * 10000) % 10;
              } else if (market === "R_10" || market === "R_25") {
                digit = (price * 1000) % 10;
              } else {
                digit = (price * 100) % 10;
              }

              ticks_current.push(digit);
              digits[digit]++;

              // Count predictions
              if (digit > overPrediction) overCount++;
              if (digit < underPrediction) underCount++;
            });

            // Mark the last digit as current if we have data
            if (ticks_current.length > 0) {
              setCurrentDigit(ticks_current[ticks_current.length - 1]);
            }

            updateDigitStats(digits);
            updateStats(overCount, underCount);

            // Update over/under prediction chart
            updateOverUnderPredictionChart(digits);

            // Update pattern display with prediction-based patterns
            updatePatternDisplay(ticks_current.slice(-50).map(d => ({
              char: d > overPrediction ? 'Ov' : (d < underPrediction ? 'U' : d),
              class: d > overPrediction ? 'Ov' : (d < underPrediction ? 'U' : `digit-${d}`),
              tooltip: `Digit: ${d} (${d > overPrediction ? 'Over Prediction' : d < underPrediction ? 'Under Prediction' : 'Neutral'})`
            })));

            ws.send(JSON.stringify({ ticks: market, subscribe: 1 }));
          } 
          else if (data.msg_type === "tick") {
            if (data.echo_req.ticks === market) {
              document.getElementById("subid").value = data.subscription.id;
              const digits = Array(10).fill(0);
              let overCount = 0, underCount = 0;
              let digit = 0;
              const quote = data.tick.quote;

              const overPrediction = parseInt(document.getElementById("digit_over_prediction").value);
              const underPrediction = parseInt(document.getElementById("digit_under_prediction").value);

              // Process digit and format display
              if (market === "RDBEAR" || market === "RDBULL" || market === "R_50" || market === "R_75") {
                digit = (quote * 10000) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 4);
              } else if (market === "R_10" || market === "R_25") {
                digit = (quote * 1000) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 3);
              } else {
                digit = (quote * 100) % 10;
                document.getElementById("current").innerHTML = formatQuote(quote, 2);
              }

              // Update current value color based on prediction
              const currentColor = digit > overPrediction ? "secondary" : (digit < underPrediction ? "danger" : "primary");
              updateCurrentValueColor(currentColor);

              // Maintain last 50 digits
              if (ticks_current.length >= 50) ticks_current.shift();
              ticks_current.push(digit);

              // Mark current digit as yellow
              setCurrentDigit(digit);

              // Count digit occurrences and predictions
              ticks_current.forEach(d => {
                digits[d]++;
                if (d > overPrediction) overCount++;
                if (d < underPrediction) underCount++;
              });

              updateDigitStats(digits);
              updateStats(overCount, underCount);

              // Update over/under prediction chart
              updateOverUnderPredictionChart(digits);

              // Update pattern display with colored digits
              updatePatternDisplay(ticks_current.slice(-50).map(d => ({
                char: d,
                class: `digit-${d}`,
                tooltip: `Digit: ${d}`
              })));
            }
          } 
          else if (data.error) {
            console.error("WebSocket error:", data.error.message);
          }
        } catch (error) {
          console.error("Error processing WebSocket message:", error);
        }
      };
      
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    };

    const changeContractType = () => {
      const type = document.getElementById("contract_type").value;
      document.getElementById("over_under").style.display = type === "2" ? "block" : "none";
      document.getElementById("digit_frequency_predictions").style.display = type === "3" ? "block" : "none";

      // Show/hide Over/Under analysis section
      toggleOverUnderSection(type === "2");

      // Show/hide Over/Under prediction comparison section for digit frequency
      document.getElementById("overUnderPredictionSection").style.display = type === "3" ? "block" : "none";

      switch(type) {
        case "0": even_odd(); break;
        case "1": rise_fall(); break;
        case "2": over_under(); break;
        case "3": match_differs(); break;
      }
    };

    const changeMarket = () => {
      removeAllClients();
      changeContractType();
    };

    // Update digit predictions for frequency analysis
    const updateDigitPredictions = () => {
      const overPrediction = parseInt(document.getElementById("digit_over_prediction").value);
      const underPrediction = parseInt(document.getElementById("digit_under_prediction").value);

      // Validate inputs
      if (overPrediction < 0 || overPrediction > 9) {
        document.getElementById("digit_over_prediction").value = Math.max(0, Math.min(9, overPrediction));
      }
      if (underPrediction < 0 || underPrediction > 9) {
        document.getElementById("digit_under_prediction").value = Math.max(0, Math.min(9, underPrediction));
      }

      // If currently on digit frequency mode, refresh the analysis
      if (document.getElementById("contract_type").value === "3") {
        match_differs();
        // updateOverUnderPredictionChart will be called by match_differs with the updated data
      }
    };

    // Update Over/Under Prediction Comparison Chart
    const updateOverUnderPredictionChart = (digits = null) => {
      const overPrediction = parseInt(document.getElementById("digit_over_prediction").value);
      const underPrediction = parseInt(document.getElementById("digit_under_prediction").value);

      // Get digit counts - either from parameter or from hidden inputs
      let digitCounts;
      if (digits && Array.isArray(digits)) {
        digitCounts = digits;
      } else {
        digitCounts = [];
        for (let i = 0; i <= 9; i++) {
          const count = parseInt(document.getElementById(`digit${i}`).value) || 0;
          digitCounts.push(count);
        }
      }

      // Calculate total digits
      const totalDigits = digitCounts.reduce((sum, count) => sum + count, 0);

      if (totalDigits === 0) {
        // Reset to default values if no data
        updatePredictionDisplay(40.9, 59.1, 0, 0, 0);
        return;
      }

      // Count digits that match over/under predictions
      let overCount = 0;
      let underCount = 0;

      for (let digit = 0; digit <= 9; digit++) {
        const count = digitCounts[digit];
        if (digit > overPrediction) {
          overCount += count;
        }
        if (digit < underPrediction) {
          underCount += count;
        }
      }

      // Calculate percentages
      const overPercentage = totalDigits > 0 ? (overCount / totalDigits * 100) : 0;
      const underPercentage = totalDigits > 0 ? (underCount / totalDigits * 100) : 0;

      // Update the display
      updatePredictionDisplay(overPercentage, underPercentage, overCount, underCount, totalDigits);
    };

    // Update prediction display elements
    const updatePredictionDisplay = (overPercentage, underPercentage, overCount, underCount, totalDigits) => {
      // Update percentages
      document.getElementById("over_prediction_percentage").textContent = `${overPercentage.toFixed(1)}%`;
      document.getElementById("under_prediction_percentage").textContent = `${underPercentage.toFixed(1)}%`;

      // Update progress bars
      const overBar = document.getElementById("over_prediction_bar");
      const underBar = document.getElementById("under_prediction_bar");

      overBar.style.width = `${overPercentage}%`;
      overBar.querySelector(".prediction-progress-text").textContent = `${overPercentage.toFixed(1)}%`;

      underBar.style.width = `${underPercentage}%`;
      underBar.querySelector(".prediction-progress-text").textContent = `${underPercentage.toFixed(1)}%`;

      // Update statistics
      document.getElementById("prediction_total_digits").textContent = totalDigits;
      document.getElementById("over_prediction_count").textContent = overCount;
      document.getElementById("under_prediction_count").textContent = underCount;

      // Calculate accuracy (this could be enhanced based on specific requirements)
      const accuracy = totalDigits > 0 ? Math.max(overPercentage, underPercentage) : 0;
      document.getElementById("prediction_accuracy").textContent = `${accuracy.toFixed(1)}%`;
    };

    // Theme toggle functionality
    const toggleTheme = () => {
      const body = document.body;
      const currentTheme = body.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

      body.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
    };

    // Load saved theme
    const loadTheme = () => {
      const savedTheme = localStorage.getItem('theme') || 'light';
      document.body.setAttribute('data-theme', savedTheme);
    };

    // Initialize the tool
    document.addEventListener('DOMContentLoaded', () => {
      // Load theme first
      loadTheme();

      // Initialize tooltips
      const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      tooltipTriggerList.map(el => new bootstrap.Tooltip(el));

      changeContractType();
      
      // Check for ticks value changes
      setInterval(() => {
        const oldTicks = document.getElementById("old_ticks").value;
        const currentTicks = document.getElementById("ticks").value;
        
        if (currentTicks > 5000) {
          document.getElementById("ticks").value = 5000;
          return;
        }
        
        if (oldTicks !== currentTicks) {
          document.getElementById("old_ticks").value = currentTicks;
          changeMarket();
        }
      }, 1000);
      
      // Refresh data every 10 minutes
      setInterval(changeMarket, 600000);
    });

    // Probability card functions
    const selectProbabilityCard = (card) => {
      // Remove selected class from all cards
      document.querySelectorAll('.probability-card').forEach(c => {
        c.classList.remove('selected');
      });
      
      // Add selected class to clicked card
      card.classList.add('selected');
      
      // Get card data
      const type = card.getAttribute('data-type');
      const value = card.getAttribute('data-value');
      
      // Update over/under inputs if this is an over/under card
      if (type === 'over') {
        document.getElementById('over_input').value = value;
      } else if (type === 'under') {
        document.getElementById('under_input').value = value;
      }
      
      // Trigger analysis update
      if (document.getElementById('contract_type').value === '2') {
        over_under();
      }
    };

    const toggleProbabilityMode = () => {
      const btn = document.getElementById('probabilityModeBtn');
      const currentText = btn.textContent.trim();
      
      if (currentText.includes('Real-time')) {
        btn.innerHTML = '<i class="bi bi-pause-circle me-1"></i>Static Mode';
        // Switch to static mode logic here
      } else {
        btn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Real-time Mode';
        // Switch to real-time mode logic here
      }
    };

    const refreshProbabilities = () => {
      // Add loading state
      const btn = event.target.closest('button');
      const originalContent = btn.innerHTML;
      btn.innerHTML = '<i class="bi bi-arrow-repeat spin"></i>';
      btn.disabled = true;
      
      // Simulate refresh (replace with actual refresh logic)
      setTimeout(() => {
        btn.innerHTML = originalContent;
        btn.disabled = false;
        
        // Update probability values with some random variation for demo
        document.querySelectorAll('.probability-value').forEach(el => {
          const currentValue = parseFloat(el.textContent);
          const variation = (Math.random() - 0.5) * 5; // ±2.5% variation
          const newValue = Math.max(0, Math.min(100, currentValue + variation));
          el.textContent = newValue.toFixed(1) + '%';
          
          // Update corresponding bar
          const card = el.closest('.probability-card');
          const bar = card.querySelector('.probability-fill');
          if (bar) {
            bar.style.width = newValue + '%';
          }
        });
      }, 1000);
    };

    // Add CSS for spinning animation
    const style = document.createElement('style');
    style.textContent = `
      .spin {
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    // Over/Under Analysis Functions
    const updateOverUnderAnalysis = () => {
      const overValue = parseInt(document.getElementById('over_value_input').value);
      const underValue = parseInt(document.getElementById('under_value_input').value);
      
      // Update display values
      document.getElementById('over_display_value').textContent = overValue;
      document.getElementById('under_display_value').textContent = underValue;
      
      // Update the main over/under inputs if they exist
      const overInput = document.getElementById('over_input');
      const underInput = document.getElementById('under_input');
      if (overInput) overInput.value = overValue;
      if (underInput) underInput.value = underValue;
      
      // Trigger analysis update if over/under mode is active
      if (document.getElementById('contract_type').value === '2') {
        over_under();
      }
      
      // Calculate percentages from current tick data if available
      if (ticks_current && ticks_current.length > 0) {
        let overCount = 0;
        let underCount = 0;
        
        ticks_current.forEach(digit => {
          if (digit > overValue) overCount++;
          if (digit < underValue) underCount++;
        });
        
        const total = ticks_current.length;
        const overPercentage = total > 0 ? ((overCount / total) * 100).toFixed(1) : 0;
        const underPercentage = total > 0 ? ((underCount / total) * 100).toFixed(1) : 0;
        
        // Update the display
        document.getElementById('over_percentage').textContent = overPercentage + '%';
        document.getElementById('under_percentage').textContent = underPercentage + '%';
        document.getElementById('over_progress_bar').style.width = overPercentage + '%';
        document.getElementById('under_progress_bar').style.width = underPercentage + '%';
        document.getElementById('over_progress_bar').querySelector('.progress-text').textContent = overPercentage + '%';
        document.getElementById('under_progress_bar').querySelector('.progress-text').textContent = underPercentage + '%';
        
        // Update statistics
        document.getElementById('total_ticks_analyzed').textContent = total;
        document.getElementById('over_count').textContent = overCount;
        document.getElementById('under_count').textContent = underCount;
      }
    };

    // Show/hide Over/Under section based on contract type
    const toggleOverUnderSection = (show) => {
      const section = document.getElementById('overUnderSection');
      if (section) {
        section.style.display = show ? 'block' : 'none';
        if (show) {
          updateOverUnderAnalysis();
        }
      }
    };
  </script>
</body>
</html>
